"""
Configuration settings for the FastAPI video analysis server.
"""

import os
from functools import lru_cache
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Server configuration
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    DEBUG: bool = Field(default=False, description="Debug mode")
    
    # Security settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "*"],
        description="Allowed hosts"
    )
    
    # Rate limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=10, description="Rate limit per minute per IP")
    RATE_LIMIT_PER_HOUR: int = Field(default=100, description="Rate limit per hour per IP")
    
    # File upload settings
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, description="Max file size in bytes (100MB)")
    ALLOWED_VIDEO_EXTENSIONS: List[str] = Field(
        default=[".mp4", ".avi", ".mov", ".mkv", ".webm"],
        description="Allowed video file extensions"
    )
    UPLOAD_DIR: str = Field(default="uploads", description="Directory for uploaded files")
    TEMP_DIR: str = Field(default="temp", description="Directory for temporary files")
    
    # Processing settings
    MAX_CONCURRENT_TASKS: int = Field(default=5, description="Maximum concurrent video processing tasks")
    TASK_TIMEOUT_SECONDS: int = Field(default=300, description="Task timeout in seconds (5 minutes)")
    CLEANUP_INTERVAL_SECONDS: int = Field(default=3600, description="Cleanup interval in seconds (1 hour)")
    
    # OpenAI settings
    OPENAI_API_KEY: str = Field(default="", description="OpenAI API key")
    OUR_OPENAI_API_KEY: str = Field(default="", description="Alternative OpenAI API key")
    OPENAI_MAX_RETRIES: int = Field(default=3, description="Maximum OpenAI API retries")
    OPENAI_TIMEOUT_SECONDS: int = Field(default=60, description="OpenAI API timeout")
    
    # Video processing settings
    MAX_FRAMES: int = Field(default=230, description="Maximum frames to extract from video")
    FRAME_QUALITY: int = Field(default=80, description="JPEG quality for frame encoding")
    FRAME_RESIZE_FACTOR: float = Field(default=0.5, description="Frame resize factor")
    
    # Logging settings
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(default="json", description="Log format (json or text)")
    
    # Redis settings (for production task queue)
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis URL for task queue")
    USE_REDIS: bool = Field(default=False, description="Use Redis for task storage")
    
    # Monitoring settings
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=8001, description="Metrics server port")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()


# Create directories if they don't exist
def ensure_directories():
    """Ensure required directories exist."""
    settings = get_settings()
    
    directories = [
        settings.UPLOAD_DIR,
        settings.TEMP_DIR,
        "saved_frames",  # Required by existing video analysis code
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# Initialize directories on import
ensure_directories()
