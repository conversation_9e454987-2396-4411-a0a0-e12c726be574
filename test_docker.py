#!/usr/bin/env python3
"""
Test script to verify Docker container functionality.
"""

import time
import requests
import json
import sys

def test_health_endpoint(base_url="http://localhost:8000"):
    """Test the health endpoint."""
    try:
        print("Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {data.get('status')}")
            print(f"   Version: {data.get('version')}")
            
            services = data.get('services', {})
            for service, status in services.items():
                print(f"   {service}: {status}")
            
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - is it running?")
        return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_detailed_health(base_url="http://localhost:8000"):
    """Test the detailed health endpoint."""
    try:
        print("\nTesting detailed health endpoint...")
        response = requests.get(f"{base_url}/health/detailed", timeout=10)
        
        if response.status_code in [200, 503]:
            data = response.json()
            print(f"✅ Detailed health check completed (status: {response.status_code})")
            print(f"   Overall status: {data.get('overall_status')}")
            
            checks = data.get('checks', {})
            for check_name, check_data in checks.items():
                if isinstance(check_data, dict) and 'status' in check_data:
                    print(f"   {check_name}: {check_data['status']} - {check_data.get('message', '')}")
                else:
                    print(f"   {check_name}: {check_data}")
            
            return True
        else:
            print(f"❌ Detailed health check failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Detailed health check error: {e}")
        return False

def test_stats_endpoint(base_url="http://localhost:8000"):
    """Test the stats endpoint."""
    try:
        print("\nTesting stats endpoint...")
        response = requests.get(f"{base_url}/stats", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Stats endpoint working")
            
            system_stats = data.get('system', {})
            print(f"   Uptime: {system_stats.get('uptime_seconds', 0):.1f} seconds")
            print(f"   Active tasks: {system_stats.get('active_tasks', 0)}")
            
            task_stats = data.get('tasks', {})
            print(f"   Total tasks: {task_stats.get('total_tasks', 0)}")
            print(f"   Completed tasks: {task_stats.get('completed_tasks', 0)}")
            
            return True
        else:
            print(f"❌ Stats endpoint failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Stats endpoint error: {e}")
        return False

def test_openapi_docs(base_url="http://localhost:8000"):
    """Test that OpenAPI documentation is accessible."""
    try:
        print("\nTesting OpenAPI documentation...")
        
        # Test OpenAPI JSON
        response = requests.get(f"{base_url}/openapi.json", timeout=10)
        if response.status_code == 200:
            print("✅ OpenAPI JSON accessible")
        else:
            print(f"❌ OpenAPI JSON failed: {response.status_code}")
            return False
        
        # Test Swagger UI (just check if it returns HTML)
        response = requests.get(f"{base_url}/docs", timeout=10)
        if response.status_code == 200 and "swagger" in response.text.lower():
            print("✅ Swagger UI accessible")
        else:
            print(f"❌ Swagger UI failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAPI docs error: {e}")
        return False

def test_invalid_endpoint(base_url="http://localhost:8000"):
    """Test that invalid endpoints return proper 404."""
    try:
        print("\nTesting invalid endpoint handling...")
        response = requests.get(f"{base_url}/nonexistent", timeout=10)
        
        if response.status_code == 404:
            print("✅ 404 handling working correctly")
            return True
        else:
            print(f"❌ Expected 404, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Invalid endpoint test error: {e}")
        return False

def wait_for_server(base_url="http://localhost:8000", max_wait=60):
    """Wait for server to be ready."""
    print(f"Waiting for server at {base_url} to be ready...")
    
    for i in range(max_wait):
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code in [200, 503]:  # 503 is OK if services are starting
                print(f"✅ Server is responding after {i+1} seconds")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 10 == 9:  # Print progress every 10 seconds
            print(f"   Still waiting... ({i+1}/{max_wait})")
    
    print(f"❌ Server did not respond within {max_wait} seconds")
    return False

def main():
    """Run all tests."""
    print("🐳 Docker Container Test Suite")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Wait for server to be ready
    if not wait_for_server(base_url):
        print("❌ Server is not responding. Make sure the container is running.")
        sys.exit(1)
    
    # Run tests
    tests = [
        test_health_endpoint,
        test_detailed_health,
        test_stats_endpoint,
        test_openapi_docs,
        test_invalid_endpoint,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func(base_url):
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The container is working correctly.")
        sys.exit(0)
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
