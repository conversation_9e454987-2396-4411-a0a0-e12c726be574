"""
Production-ready FastAPI server for video analysis.
Provides endpoints for analyzing listening and responding videos.
"""

import asyncio
import logging
import os
import time
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any, Optional, List

import structlog
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends, Request, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware

from models import (
    VideoAnalysisRequest,
    VideoAnalysisResponse,
    VideoPathRequest,
    ListeningAnalysisResponse,
    RespondingAnalysisResponse,
    TaskStatusResponse,
    ErrorResponse,
    HealthCheckResponse,
    AnalysisType
)
from services import VideoAnalysisService
from config import get_settings
from monitoring import metrics_collector, get_metrics_response, get_comprehensive_health

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Rate limiting setup
limiter = Limiter(key_func=get_remote_address)

# Global task storage (in production, use Redis or database)
task_storage: Dict[str, Dict[str, Any]] = {}

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting video analysis API server")
    
    # Initialize services
    app.state.video_service = VideoAnalysisService()
    
    # Verify dependencies
    try:
        # Test OpenAI connection
        await app.state.video_service.health_check()
        logger.info("All services initialized successfully")
    except Exception as e:
        logger.error("Failed to initialize services", error=str(e))
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down video analysis API server")
    # Cleanup resources if needed


# Create FastAPI application
app = FastAPI(
    title="Video Analysis API",
    description="Production-ready API for analyzing listening and responding videos",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(SlowAPIMiddleware)

# Add rate limiting error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Log all requests and responses."""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # Log request
    logger.info(
        "Request started",
        request_id=request_id,
        method=request.method,
        url=str(request.url),
        client_ip=get_remote_address(request)
    )
    
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(
        "Request completed",
        request_id=request_id,
        status_code=response.status_code,
        process_time=process_time
    )
    
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler for unhandled errors."""
    logger.error(
        "Unhandled exception",
        error=str(exc),
        path=request.url.path,
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="Internal server error",
            message="An unexpected error occurred",
            request_id=request.headers.get("X-Request-ID", "unknown")
        ).dict()
    )


def get_video_service() -> VideoAnalysisService:
    """Dependency to get video analysis service."""
    return app.state.video_service


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Basic health check endpoint."""
    try:
        service = get_video_service()
        await service.health_check()

        stats = metrics_collector.get_system_stats()

        return HealthCheckResponse(
            status="healthy",
            timestamp=time.time(),
            version="1.0.0",
            services={
                "openai": "connected",
                "video_processing": "available"
            },
            uptime_seconds=stats["uptime_seconds"]
        )
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=503,
            detail="Service unavailable"
        )


@app.get("/health/detailed")
async def detailed_health_check():
    """Comprehensive health check with detailed system information."""
    try:
        health_info = await get_comprehensive_health()

        status_code = 200
        if health_info["overall_status"] == "unhealthy":
            status_code = 503
        elif health_info["overall_status"] == "warning":
            status_code = 200  # Still operational but with warnings

        return JSONResponse(
            status_code=status_code,
            content=health_info
        )
    except Exception as e:
        logger.error("Detailed health check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={
                "overall_status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint."""
    if not settings.ENABLE_METRICS:
        raise HTTPException(status_code=404, detail="Metrics disabled")

    return get_metrics_response()


@app.get("/stats")
async def get_stats():
    """Get current system statistics."""
    stats = metrics_collector.get_system_stats()

    # Add task storage statistics
    task_stats = {
        "total_tasks": len(task_storage),
        "pending_tasks": len([t for t in task_storage.values() if t["status"] == "pending"]),
        "processing_tasks": len([t for t in task_storage.values() if t["status"] == "processing"]),
        "completed_tasks": len([t for t in task_storage.values() if t["status"] == "completed"]),
        "failed_tasks": len([t for t in task_storage.values() if t["status"] == "failed"]),
        "cancelled_tasks": len([t for t in task_storage.values() if t["status"] == "cancelled"])
    }

    return {
        "system": stats,
        "tasks": task_stats,
        "timestamp": time.time()
    }


@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Video Analysis API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Video Analysis Endpoints

@app.post("/analyze/listening/upload", response_model=VideoAnalysisResponse)
@limiter.limit(f"{settings.RATE_LIMIT_PER_MINUTE}/minute")
async def analyze_listening_upload(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    callback_url: Optional[str] = None,
    service: VideoAnalysisService = Depends(get_video_service)
):
    """Analyze listening video from uploaded file."""
    task_id = str(uuid.uuid4())

    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file extension
        _, ext = os.path.splitext(file.filename.lower())
        if ext not in settings.ALLOWED_VIDEO_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file format. Allowed: {', '.join(settings.ALLOWED_VIDEO_EXTENSIONS)}"
            )

        # Read file content
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Record file upload metrics
        metrics_collector.record_file_upload(len(file_content))

        # Record file upload metrics
        metrics_collector.record_file_upload(len(file_content))

        # Save uploaded file
        file_path = await service.save_uploaded_file(file_content, file.filename)

        # Create task record
        task_record = {
            "task_id": task_id,
            "status": "pending",
            "analysis_type": "listening",
            "created_at": time.time(),
            "file_path": file_path,
            "callback_url": callback_url
        }
        task_storage[task_id] = task_record

        # Start background processing
        background_tasks.add_task(
            process_video_analysis,
            task_id,
            file_path,
            AnalysisType.LISTENING,
            service,
            cleanup_file=True
        )

        logger.info("Listening analysis task created", task_id=task_id, filename=file.filename)

        return VideoAnalysisResponse(
            task_id=task_id,
            analysis_type=AnalysisType.LISTENING,
            status="pending",
            created_at=datetime.utcnow()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create listening analysis task", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create analysis task")


@app.post("/analyze/listening/path", response_model=VideoAnalysisResponse)
@limiter.limit(f"{settings.RATE_LIMIT_PER_MINUTE}/minute")
async def analyze_listening_path(
    request: Request,
    background_tasks: BackgroundTasks,
    video_request: VideoPathRequest,
    service: VideoAnalysisService = Depends(get_video_service)
):
    """Analyze listening video from file path."""
    task_id = str(uuid.uuid4())

    try:
        # Create task record
        task_record = {
            "task_id": task_id,
            "status": "pending",
            "analysis_type": "listening",
            "created_at": time.time(),
            "file_path": video_request.video_path,
            "callback_url": video_request.callback_url
        }
        task_storage[task_id] = task_record

        # Start background processing
        background_tasks.add_task(
            process_video_analysis,
            task_id,
            video_request.video_path,
            AnalysisType.LISTENING,
            service,
            cleanup_file=False
        )

        logger.info("Listening analysis task created", task_id=task_id, video_path=video_request.video_path)

        return VideoAnalysisResponse(
            task_id=task_id,
            analysis_type=AnalysisType.LISTENING,
            status="pending",
            created_at=datetime.utcnow()
        )

    except Exception as e:
        logger.error("Failed to create listening analysis task", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create analysis task")


@app.post("/analyze/responding/upload", response_model=VideoAnalysisResponse)
@limiter.limit(f"{settings.RATE_LIMIT_PER_MINUTE}/minute")
async def analyze_responding_upload(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    callback_url: Optional[str] = None,
    service: VideoAnalysisService = Depends(get_video_service)
):
    """Analyze responding video from uploaded file."""
    task_id = str(uuid.uuid4())

    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")

        # Check file extension
        _, ext = os.path.splitext(file.filename.lower())
        if ext not in settings.ALLOWED_VIDEO_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file format. Allowed: {', '.join(settings.ALLOWED_VIDEO_EXTENSIONS)}"
            )

        # Read file content
        file_content = await file.read()
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
            )

        # Record file upload metrics
        metrics_collector.record_file_upload(len(file_content))

        # Save uploaded file
        file_path = await service.save_uploaded_file(file_content, file.filename)

        # Create task record
        task_record = {
            "task_id": task_id,
            "status": "pending",
            "analysis_type": "responding",
            "created_at": time.time(),
            "file_path": file_path,
            "callback_url": callback_url
        }
        task_storage[task_id] = task_record

        # Start background processing
        background_tasks.add_task(
            process_video_analysis,
            task_id,
            file_path,
            AnalysisType.RESPONDING,
            service,
            cleanup_file=True
        )

        logger.info("Responding analysis task created", task_id=task_id, filename=file.filename)

        return VideoAnalysisResponse(
            task_id=task_id,
            analysis_type=AnalysisType.RESPONDING,
            status="pending",
            created_at=datetime.utcnow()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create responding analysis task", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create analysis task")


@app.post("/analyze/responding/path", response_model=VideoAnalysisResponse)
@limiter.limit(f"{settings.RATE_LIMIT_PER_MINUTE}/minute")
async def analyze_responding_path(
    request: Request,
    background_tasks: BackgroundTasks,
    video_request: VideoPathRequest,
    service: VideoAnalysisService = Depends(get_video_service)
):
    """Analyze responding video from file path."""
    task_id = str(uuid.uuid4())

    try:
        # Create task record
        task_record = {
            "task_id": task_id,
            "status": "pending",
            "analysis_type": "responding",
            "created_at": time.time(),
            "file_path": video_request.video_path,
            "callback_url": video_request.callback_url
        }
        task_storage[task_id] = task_record

        # Start background processing
        background_tasks.add_task(
            process_video_analysis,
            task_id,
            video_request.video_path,
            AnalysisType.RESPONDING,
            service,
            cleanup_file=False
        )

        logger.info("Responding analysis task created", task_id=task_id, video_path=video_request.video_path)

        return VideoAnalysisResponse(
            task_id=task_id,
            analysis_type=AnalysisType.RESPONDING,
            status="pending",
            created_at=datetime.utcnow()
        )

    except Exception as e:
        logger.error("Failed to create responding analysis task", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create analysis task")


@app.get("/tasks/{task_id}", response_model=VideoAnalysisResponse)
async def get_task_status(task_id: str):
    """Get the status and result of a video analysis task."""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_record = task_storage[task_id]

    return VideoAnalysisResponse(
        task_id=task_id,
        analysis_type=AnalysisType(task_record["analysis_type"]),
        status=task_record["status"],
        result=task_record.get("result"),
        created_at=datetime.fromtimestamp(task_record["created_at"]),
        completed_at=datetime.fromtimestamp(task_record["completed_at"]) if task_record.get("completed_at") else None,
        error_message=task_record.get("error_message"),
        processing_time_seconds=task_record.get("processing_time")
    )


@app.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """Cancel a pending video analysis task."""
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="Task not found")

    task_record = task_storage[task_id]

    if task_record["status"] in ["completed", "failed", "cancelled"]:
        raise HTTPException(status_code=400, detail="Task cannot be cancelled")

    task_record["status"] = "cancelled"
    task_record["completed_at"] = time.time()

    logger.info("Task cancelled", task_id=task_id)

    return {"message": "Task cancelled successfully", "task_id": task_id}


@app.get("/tasks", response_model=List[VideoAnalysisResponse])
async def list_tasks(
    status: Optional[str] = None,
    analysis_type: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """List video analysis tasks with optional filtering."""
    tasks = []

    for task_id, task_record in task_storage.items():
        # Apply filters
        if status and task_record["status"] != status:
            continue
        if analysis_type and task_record["analysis_type"] != analysis_type:
            continue

        tasks.append(VideoAnalysisResponse(
            task_id=task_id,
            analysis_type=AnalysisType(task_record["analysis_type"]),
            status=task_record["status"],
            result=task_record.get("result"),
            created_at=datetime.fromtimestamp(task_record["created_at"]),
            completed_at=datetime.fromtimestamp(task_record["completed_at"]) if task_record.get("completed_at") else None,
            error_message=task_record.get("error_message"),
            processing_time_seconds=task_record.get("processing_time")
        ))

    # Sort by creation time (newest first)
    tasks.sort(key=lambda x: x.created_at, reverse=True)

    # Apply pagination
    return tasks[offset:offset + limit]


# Background task processing function
async def process_video_analysis(
    task_id: str,
    video_path: str,
    analysis_type: AnalysisType,
    service: VideoAnalysisService,
    cleanup_file: bool = False
):
    """Background task for processing video analysis."""
    start_time = time.time()

    # Record analysis start
    metrics_collector.record_analysis_start(analysis_type.value)

    try:
        # Update task status to processing
        if task_id in task_storage:
            task_storage[task_id]["status"] = "processing"
            task_storage[task_id]["started_at"] = start_time

        logger.info("Starting background video analysis", task_id=task_id, video_path=video_path)

        # Perform analysis
        result = await service.analyze_video_async(video_path, analysis_type)

        # Update task with result
        if task_id in task_storage:
            task_storage[task_id].update({
                "status": "completed",
                "result": result["result"],
                "completed_at": time.time(),
                "processing_time": result["processing_time"]
            })

        # Record successful completion
        metrics_collector.record_analysis_complete(
            analysis_type.value,
            "completed",
            result["processing_time"]
        )

        logger.info("Background video analysis completed", task_id=task_id, processing_time=result["processing_time"])

    except Exception as e:
        processing_time = time.time() - start_time

        # Update task with error
        if task_id in task_storage:
            task_storage[task_id].update({
                "status": "failed",
                "error_message": str(e),
                "completed_at": time.time(),
                "processing_time": processing_time
            })

        # Record failed completion
        metrics_collector.record_analysis_complete(
            analysis_type.value,
            "failed",
            processing_time
        )

        # Record error
        metrics_collector.record_error("analysis_error", f"/analyze/{analysis_type.value}")

        logger.error("Background video analysis failed", task_id=task_id, error=str(e), exc_info=True)

    finally:
        # Cleanup uploaded file if requested
        if cleanup_file:
            try:
                await service.cleanup_temp_files(video_path)
            except Exception as e:
                logger.warning("Failed to cleanup file", video_path=video_path, error=str(e))


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )
