# Video Analysis API

A production-ready FastAPI server for analyzing listening and responding videos using AI-powered evaluation.

## Features

- **RESTful API** for video analysis with file upload and file path support
- **Async processing** with background tasks for long-running operations
- **Rate limiting** to prevent abuse and ensure fair usage
- **Comprehensive monitoring** with health checks and metrics
- **Production-ready** with proper logging, error handling, and security
- **Auto-generated documentation** with Swagger/OpenAPI integration
- **Scalable architecture** with connection pooling and request limits

## Quick Start

### Prerequisites

- Python 3.8+
- FFmpeg (for video processing)
- OpenAI API key

### Installation

#### Option 1: Docker (Recommended)

1. Clone the repository:

```bash
git clone <repository-url>
cd gem-demo
```

2. Set your OpenAI API key in docker-compose.yml or create a .env file:

```bash
echo "OPENAI_API_KEY=your_openai_api_key_here" > .env
```

3. Start with Docker Compose:

```bash
docker-compose up --build
```

The API will be available at `http://localhost:8000`

#### Option 2: Local Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd gem-demo
```

2. Install system dependencies:

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install ffmpeg

# macOS
brew install ffmpeg

# Windows
# Download ffmpeg from https://ffmpeg.org/download.html
```

3. Install Python dependencies:

```bash
pip install -r requirements.txt
```

4. Create environment configuration:

```bash
python run_server.py --create-env
cp .env.example .env
```

5. Edit `.env` file and add your OpenAI API key:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

6. Debug imports (optional):

```bash
python debug_imports.py
```

7. Start the server:

```bash
python run_server.py
```

The API will be available at `http://localhost:8000`

## API Documentation

### Interactive Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Core Endpoints

#### Health Check

```http
GET /health
```

Basic health check endpoint.

```http
GET /health/detailed
```

Comprehensive health check with system information.

#### Video Analysis

##### Upload and Analyze (Listening)

```http
POST /analyze/listening/upload
Content-Type: multipart/form-data

file: <video_file>
callback_url: <optional_callback_url>
```

##### Analyze by Path (Listening)

```http
POST /analyze/listening/path
Content-Type: application/json

{
  "analysis_type": "listening",
  "video_path": "/path/to/video.mp4",
  "callback_url": "https://example.com/callback"
}
```

##### Upload and Analyze (Responding)

```http
POST /analyze/responding/upload
Content-Type: multipart/form-data

file: <video_file>
callback_url: <optional_callback_url>
```

##### Analyze by Path (Responding)

```http
POST /analyze/responding/path
Content-Type: application/json

{
  "analysis_type": "responding",
  "video_path": "/path/to/video.mp4",
  "callback_url": "https://example.com/callback"
}
```

#### Task Management

##### Get Task Status

```http
GET /tasks/{task_id}
```

##### List Tasks

```http
GET /tasks?status=completed&analysis_type=listening&limit=50&offset=0
```

##### Cancel Task

```http
DELETE /tasks/{task_id}
```

#### Monitoring

##### System Statistics

```http
GET /stats
```

##### Prometheus Metrics

```http
GET /metrics
```

## Response Formats

### Listening Analysis Response

```json
{
  "listeningEvaluation": {
    "eyeContact": {
      "score": 4,
      "explanation": "Good eye contact maintained throughout"
    },
    "facialExpressions": {
      "score": 4,
      "explanation": "Appropriate facial expressions showing engagement"
    },
    "headShouldersAlignment": {
      "score": 3,
      "explanation": "Generally good posture with some slouching"
    },
    "microGestures": {
      "score": 4,
      "explanation": "Good use of nodding and supportive gestures"
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Strong eye contact and engagement",
  "improvements": "Focus on maintaining better posture"
}
```

### Responding Analysis Response

```json
{
  "respondingEvaluation": {
    "visual": {
      "eyeContact": { "score": 4, "explanation": "..." },
      "facialExpressions": { "score": 4, "explanation": "..." },
      "headShouldersAlignment": { "score": 3, "explanation": "..." },
      "microGestures": { "score": 4, "explanation": "..." }
    },
    "vocal": {
      "toneEmotion": { "score": 4, "explanation": "..." },
      "volumeClarity": { "score": 4, "explanation": "..." },
      "paceRhythm": { "score": 3, "explanation": "..." }
    },
    "content": {
      "messageRelevanceAccuracy": { "score": 4, "explanation": "..." },
      "structureCoherence": { "score": 4, "explanation": "..." },
      "empathyWordChoice": { "score": 4, "explanation": "..." }
    }
  },
  "overallScore": 4,
  "starRating": "★★★★☆",
  "strengths": "Strong communication skills",
  "improvements": "Work on pacing and rhythm"
}
```

## Configuration

### Environment Variables

| Variable                | Default     | Description                     |
| ----------------------- | ----------- | ------------------------------- |
| `HOST`                  | `0.0.0.0`   | Server host                     |
| `PORT`                  | `8000`      | Server port                     |
| `DEBUG`                 | `false`     | Debug mode                      |
| `OPENAI_API_KEY`        | -           | OpenAI API key (required)       |
| `RATE_LIMIT_PER_MINUTE` | `10`        | Rate limit per minute per IP    |
| `MAX_FILE_SIZE`         | `104857600` | Max file size (100MB)           |
| `MAX_CONCURRENT_TASKS`  | `5`         | Max concurrent processing tasks |
| `TASK_TIMEOUT_SECONDS`  | `300`       | Task timeout (5 minutes)        |

See `.env.example` for complete configuration options.

## Production Deployment

### Using Docker (Recommended)

```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
CMD ["python", "run_server.py", "--workers", "4"]
```

### Using systemd

Create `/etc/systemd/system/video-analysis-api.service`:

```ini
[Unit]
Description=Video Analysis API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/app
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/python run_server.py --workers 4
Restart=always

[Install]
WantedBy=multi-user.target
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase timeouts for video processing
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;

        # Increase max body size for video uploads
        client_max_body_size 100M;
    }
}
```

## Monitoring and Observability

### Health Checks

- Basic: `GET /health`
- Detailed: `GET /health/detailed`
- System stats: `GET /stats`

### Metrics

Prometheus metrics available at `/metrics`:

- Request counts and durations
- Analysis task metrics
- Error rates
- File upload sizes
- OpenAI API call metrics

### Logging

Structured JSON logging with:

- Request/response logging
- Error tracking with stack traces
- Performance metrics
- Task lifecycle events

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
python test_api.py

# Or use pytest directly
pytest test_api.py -v
```

## Rate Limiting

Default limits:

- 10 requests per minute per IP
- 100 requests per hour per IP

Rate limiting headers are included in responses:

- `X-RateLimit-Limit`
- `X-RateLimit-Remaining`
- `X-RateLimit-Reset`

## Error Handling

All errors return structured JSON responses:

```json
{
  "error": "validation_error",
  "message": "Invalid file format",
  "details": {...},
  "request_id": "uuid",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

Common HTTP status codes:

- `400` - Bad Request (validation errors)
- `413` - Payload Too Large (file size exceeded)
- `429` - Too Many Requests (rate limited)
- `503` - Service Unavailable (health check failed)

## Security Considerations

- CORS configuration for web clients
- Trusted host middleware
- File type validation
- File size limits
- Rate limiting
- Request ID tracking
- Structured logging for audit trails

## Troubleshooting

### Common Issues

#### 1. Librosa/Numba Import Errors

If you see errors related to `numba` caching or `librosa` imports:

```bash
# Set environment variables before starting
export NUMBA_CACHE_DIR=/tmp/numba_cache
export NUMBA_DISABLE_JIT=1
python run_server.py
```

Or use Docker (recommended) which handles this automatically.

#### 2. FFmpeg Not Found

```bash
# Install ffmpeg
sudo apt-get install ffmpeg  # Ubuntu/Debian
brew install ffmpeg          # macOS
```

#### 3. OpenAI API Key Issues

- Ensure your API key is set in the environment
- Check that the key has sufficient credits
- Verify the key format (starts with `sk-`)

#### 4. File Upload Issues

- Check file size limits (default 100MB)
- Verify video format is supported (.mp4, .avi, .mov, .mkv, .webm)
- Ensure sufficient disk space

#### 5. Docker Issues

```bash
# Rebuild containers
docker-compose down
docker-compose up --build

# Check logs
docker-compose logs video-analysis-api

# Test container health
python test_docker.py
```

### Debug Tools

1. **Import Debug**: `python debug_imports.py`
2. **Docker Test**: `python test_docker.py`
3. **Health Check**: `curl http://localhost:8000/health/detailed`
4. **Server Validation**: `python run_server.py --validate`

## Support

For issues and questions:

1. Run the debug tools above
2. Check the API documentation at `/docs`
3. Review the logs for error details
4. Use the health check endpoints to diagnose issues
5. Monitor metrics for performance insights
