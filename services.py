"""
Service layer for video analysis operations.
Wraps existing analysis functions with async support and error handling.
"""

import asyncio
import gc
import os
import tempfile
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional
import structlog

from openai import OpenAI
from models import AnalysisType, ListeningAnalysisResponse, RespondingAnalysisResponse
from config import get_settings

# Import existing analysis functions with error handling
try:
    from listening_analysis import analyze_listening_video
    LISTENING_ANALYSIS_AVAILABLE = True
except ImportError as e:
    logger.error("Failed to import listening analysis", error=str(e))
    LISTENING_ANALYSIS_AVAILABLE = False
    analyze_listening_video = None

try:
    from responding_analysis import analyze_responding_video
    RESPONDING_ANALYSIS_AVAILABLE = True
except ImportError as e:
    logger.error("Failed to import responding analysis", error=str(e))
    RESPONDING_ANALYSIS_AVAILABLE = False
    analyze_responding_video = None

logger = structlog.get_logger()


class VideoAnalysisError(Exception):
    """Custom exception for video analysis errors."""
    pass


class VideoAnalysisService:
    """Service for handling video analysis operations."""
    
    def __init__(self):
        self.settings = get_settings()
        self.executor = ThreadPoolExecutor(max_workers=self.settings.MAX_CONCURRENT_TASKS)
        self._openai_client = None
        
    @property
    def openai_client(self) -> OpenAI:
        """Lazy initialization of OpenAI client."""
        if self._openai_client is None:
            api_key = (
                self.settings.OPENAI_API_KEY or 
                self.settings.OUR_OPENAI_API_KEY or 
                os.getenv("OPENAI_API_KEY") or 
                os.getenv("OUR_OPENAI_API_KEY")
            )
            
            if not api_key:
                raise VideoAnalysisError("OpenAI API key not configured")
                
            self._openai_client = OpenAI(
                api_key=api_key,
                timeout=self.settings.OPENAI_TIMEOUT_SECONDS,
                max_retries=self.settings.OPENAI_MAX_RETRIES
            )
        
        return self._openai_client
    
    async def health_check(self) -> Dict[str, str]:
        """Perform health check on the service."""
        try:
            # Test OpenAI connection
            client = self.openai_client
            # Simple test call to verify API key works
            await asyncio.get_event_loop().run_in_executor(
                self.executor,
                lambda: client.models.list()
            )
            
            return {
                "openai": "connected",
                "video_processing": "available",
                "executor": f"available ({self.settings.MAX_CONCURRENT_TASKS} workers)",
                "listening_analysis": "available" if LISTENING_ANALYSIS_AVAILABLE else "unavailable",
                "responding_analysis": "available" if RESPONDING_ANALYSIS_AVAILABLE else "unavailable"
            }
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            raise VideoAnalysisError(f"Health check failed: {str(e)}")
    
    async def validate_video_file(self, file_path: str) -> bool:
        """Validate that the video file exists and is accessible."""
        try:
            if not os.path.exists(file_path):
                raise VideoAnalysisError(f"Video file not found: {file_path}")
            
            if not os.path.isfile(file_path):
                raise VideoAnalysisError(f"Path is not a file: {file_path}")
            
            # Check file extension
            _, ext = os.path.splitext(file_path.lower())
            if ext not in self.settings.ALLOWED_VIDEO_EXTENSIONS:
                raise VideoAnalysisError(
                    f"Unsupported video format: {ext}. "
                    f"Supported formats: {', '.join(self.settings.ALLOWED_VIDEO_EXTENSIONS)}"
                )
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size > self.settings.MAX_FILE_SIZE:
                raise VideoAnalysisError(
                    f"File too large: {file_size} bytes. "
                    f"Maximum allowed: {self.settings.MAX_FILE_SIZE} bytes"
                )
            
            return True
            
        except VideoAnalysisError:
            raise
        except Exception as e:
            logger.error("Video validation failed", file_path=file_path, error=str(e))
            raise VideoAnalysisError(f"Video validation failed: {str(e)}")
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """Save uploaded file to temporary location."""
        try:
            # Create unique filename to avoid conflicts
            timestamp = int(time.time())
            safe_filename = f"{timestamp}_{filename}"
            file_path = os.path.join(self.settings.UPLOAD_DIR, safe_filename)
            
            # Write file asynchronously
            def write_file():
                with open(file_path, "wb") as f:
                    f.write(file_content)
                return file_path
            
            result_path = await asyncio.get_event_loop().run_in_executor(
                self.executor, write_file
            )
            
            logger.info("File uploaded successfully", file_path=result_path, size=len(file_content))
            return result_path
            
        except Exception as e:
            logger.error("Failed to save uploaded file", filename=filename, error=str(e))
            raise VideoAnalysisError(f"Failed to save uploaded file: {str(e)}")
    
    async def analyze_video_async(
        self, 
        video_path: str, 
        analysis_type: AnalysisType
    ) -> Dict[str, Any]:
        """Perform video analysis asynchronously."""
        start_time = time.time()
        
        try:
            # Validate video file
            await self.validate_video_file(video_path)
            
            logger.info(
                "Starting video analysis",
                video_path=video_path,
                analysis_type=analysis_type.value
            )
            
            # Run analysis in thread pool to avoid blocking
            if analysis_type == AnalysisType.LISTENING:
                result = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self._run_listening_analysis,
                    video_path
                )
            elif analysis_type == AnalysisType.RESPONDING:
                result = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    self._run_responding_analysis,
                    video_path
                )
            else:
                raise VideoAnalysisError(f"Unsupported analysis type: {analysis_type}")
            
            processing_time = time.time() - start_time
            
            logger.info(
                "Video analysis completed",
                video_path=video_path,
                analysis_type=analysis_type.value,
                processing_time=processing_time
            )
            
            return {
                "result": result,
                "processing_time": processing_time,
                "analysis_type": analysis_type
            }
            
        except VideoAnalysisError:
            raise
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                "Video analysis failed",
                video_path=video_path,
                analysis_type=analysis_type.value,
                processing_time=processing_time,
                error=str(e),
                exc_info=True
            )
            raise VideoAnalysisError(f"Analysis failed: {str(e)}")
    
    def _run_listening_analysis(self, video_path: str) -> Dict[str, Any]:
        """Run listening analysis in thread pool."""
        if not LISTENING_ANALYSIS_AVAILABLE:
            raise VideoAnalysisError("Listening analysis is not available due to import errors")

        try:
            result = analyze_listening_video(video_path)

            # Ensure garbage collection after analysis
            gc.collect()

            return result

        except Exception as e:
            logger.error("Listening analysis failed", video_path=video_path, error=str(e))
            raise VideoAnalysisError(f"Listening analysis failed: {str(e)}")
    
    def _run_responding_analysis(self, video_path: str) -> Dict[str, Any]:
        """Run responding analysis in thread pool."""
        if not RESPONDING_ANALYSIS_AVAILABLE:
            raise VideoAnalysisError("Responding analysis is not available due to import errors")

        try:
            result = analyze_responding_video(video_path)

            # Ensure garbage collection after analysis
            gc.collect()

            return result

        except Exception as e:
            logger.error("Responding analysis failed", video_path=video_path, error=str(e))
            raise VideoAnalysisError(f"Responding analysis failed: {str(e)}")
    
    async def cleanup_temp_files(self, file_path: str) -> None:
        """Clean up temporary files after processing."""
        try:
            if os.path.exists(file_path) and file_path.startswith(self.settings.UPLOAD_DIR):
                await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    os.remove,
                    file_path
                )
                logger.info("Temporary file cleaned up", file_path=file_path)
        except Exception as e:
            logger.warning("Failed to cleanup temporary file", file_path=file_path, error=str(e))
    
    def __del__(self):
        """Cleanup executor on service destruction."""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=False)
