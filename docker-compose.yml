version: "3.8"

services:
  video-analysis-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - OPENAI_API_KEY=***************************************************
      - RATE_LIMIT_PER_MINUTE=10
      - RATE_LIMIT_PER_HOUR=100
      - MAX_CONCURRENT_TASKS=5
      - ENABLE_METRICS=true
      - USE_REDIS=true
      - REDIS_URL=redis://redis:6379/0
      # Fix Numba/Librosa caching issues
      - NUMBA_CACHE_DIR=/tmp/numba_cache
      - NUMBA_DISABLE_JIT=1
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import requests; requests.get('http://localhost:8000/health', timeout=10)",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - video-analysis-api
    restart: unless-stopped

volumes:
  redis_data:
