"""
Monitoring and metrics collection for the video analysis API.
"""

import time
from typing import Dict, Any
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from fastapi import Response
import structlog

logger = structlog.get_logger()

# Prometheus metrics
REQUEST_COUNT = Counter(
    'video_analysis_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code']
)

REQUEST_DURATION = Histogram(
    'video_analysis_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ANALYSIS_DURATION = Histogram(
    'video_analysis_processing_duration_seconds',
    'Video analysis processing duration in seconds',
    ['analysis_type']
)

ANALYSIS_COUNT = Counter(
    'video_analysis_tasks_total',
    'Total number of analysis tasks',
    ['analysis_type', 'status']
)

ACTIVE_TASKS = Gauge(
    'video_analysis_active_tasks',
    'Number of currently active analysis tasks'
)

FILE_UPLOAD_SIZE = Histogram(
    'video_analysis_upload_size_bytes',
    'Size of uploaded video files in bytes'
)

ERROR_COUNT = Counter(
    'video_analysis_errors_total',
    'Total number of errors',
    ['error_type', 'endpoint']
)

OPENAI_API_CALLS = Counter(
    'openai_api_calls_total',
    'Total number of OpenAI API calls',
    ['model', 'status']
)

OPENAI_API_DURATION = Histogram(
    'openai_api_duration_seconds',
    'OpenAI API call duration in seconds',
    ['model']
)


class MetricsCollector:
    """Centralized metrics collection."""
    
    def __init__(self):
        self.start_time = time.time()
        self.active_tasks_count = 0
    
    def record_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status_code=status_code).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_analysis_start(self, analysis_type: str):
        """Record analysis task start."""
        self.active_tasks_count += 1
        ACTIVE_TASKS.set(self.active_tasks_count)
        logger.info("Analysis task started", analysis_type=analysis_type, active_tasks=self.active_tasks_count)
    
    def record_analysis_complete(self, analysis_type: str, status: str, duration: float):
        """Record analysis task completion."""
        self.active_tasks_count = max(0, self.active_tasks_count - 1)
        ACTIVE_TASKS.set(self.active_tasks_count)
        ANALYSIS_COUNT.labels(analysis_type=analysis_type, status=status).inc()
        ANALYSIS_DURATION.labels(analysis_type=analysis_type).observe(duration)
        logger.info(
            "Analysis task completed",
            analysis_type=analysis_type,
            status=status,
            duration=duration,
            active_tasks=self.active_tasks_count
        )
    
    def record_file_upload(self, file_size: int):
        """Record file upload metrics."""
        FILE_UPLOAD_SIZE.observe(file_size)
    
    def record_error(self, error_type: str, endpoint: str):
        """Record error metrics."""
        ERROR_COUNT.labels(error_type=error_type, endpoint=endpoint).inc()
    
    def record_openai_call(self, model: str, status: str, duration: float):
        """Record OpenAI API call metrics."""
        OPENAI_API_CALLS.labels(model=model, status=status).inc()
        OPENAI_API_DURATION.labels(model=model).observe(duration)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Get current system statistics."""
        uptime = time.time() - self.start_time
        
        return {
            "uptime_seconds": uptime,
            "active_tasks": self.active_tasks_count,
            "total_requests": sum([
                metric.samples[0].value for metric in REQUEST_COUNT.collect()
                for sample in metric.samples
            ]),
            "total_analyses": sum([
                sample.value for metric in ANALYSIS_COUNT.collect()
                for sample in metric.samples
            ]),
            "total_errors": sum([
                sample.value for metric in ERROR_COUNT.collect()
                for sample in metric.samples
            ])
        }


# Global metrics collector instance
metrics_collector = MetricsCollector()


def get_metrics_response() -> Response:
    """Generate Prometheus metrics response."""
    return Response(
        content=generate_latest(),
        media_type=CONTENT_TYPE_LATEST
    )


class MetricsMiddleware:
    """Middleware for automatic metrics collection."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = time.time()
        method = scope["method"]
        path = scope["path"]
        
        # Normalize path for metrics (remove IDs)
        normalized_path = self._normalize_path(path)
        
        async def send_wrapper(message):
            if message["type"] == "http.response.start":
                status_code = message["status"]
                duration = time.time() - start_time
                
                # Record metrics
                metrics_collector.record_request(method, normalized_path, status_code, duration)
                
                # Log request completion
                logger.info(
                    "Request completed",
                    method=method,
                    path=path,
                    status_code=status_code,
                    duration=duration
                )
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
    
    def _normalize_path(self, path: str) -> str:
        """Normalize path for metrics by removing dynamic segments."""
        # Replace UUIDs and other dynamic segments
        import re
        
        # Replace task IDs (UUIDs)
        path = re.sub(r'/tasks/[a-f0-9-]{36}', '/tasks/{id}', path)
        
        # Replace other common patterns
        path = re.sub(r'/\d+', '/{id}', path)
        
        return path


# Health check functions
async def check_openai_health() -> Dict[str, str]:
    """Check OpenAI API health."""
    try:
        from services import VideoAnalysisService
        service = VideoAnalysisService()
        await service.health_check()
        return {"status": "healthy", "message": "OpenAI API accessible"}
    except Exception as e:
        return {"status": "unhealthy", "message": f"OpenAI API error: {str(e)}"}


async def check_disk_space() -> Dict[str, str]:
    """Check available disk space."""
    try:
        import shutil
        from config import get_settings
        
        settings = get_settings()
        
        # Check upload directory space
        total, used, free = shutil.disk_usage(settings.UPLOAD_DIR)
        free_gb = free // (1024**3)
        
        if free_gb < 1:  # Less than 1GB free
            return {"status": "warning", "message": f"Low disk space: {free_gb}GB free"}
        
        return {"status": "healthy", "message": f"Disk space OK: {free_gb}GB free"}
    
    except Exception as e:
        return {"status": "unhealthy", "message": f"Disk check error: {str(e)}"}


async def check_memory_usage() -> Dict[str, str]:
    """Check memory usage."""
    try:
        import psutil
        
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        if memory_percent > 90:
            return {"status": "warning", "message": f"High memory usage: {memory_percent}%"}
        
        return {"status": "healthy", "message": f"Memory usage OK: {memory_percent}%"}
    
    except ImportError:
        return {"status": "unknown", "message": "psutil not available"}
    except Exception as e:
        return {"status": "unhealthy", "message": f"Memory check error: {str(e)}"}


async def get_comprehensive_health() -> Dict[str, Any]:
    """Get comprehensive health check results."""
    health_checks = {
        "openai": await check_openai_health(),
        "disk_space": await check_disk_space(),
        "memory": await check_memory_usage(),
        "system_stats": metrics_collector.get_system_stats()
    }
    
    # Determine overall status
    statuses = [check["status"] for check in health_checks.values() if isinstance(check, dict) and "status" in check]
    
    if "unhealthy" in statuses:
        overall_status = "unhealthy"
    elif "warning" in statuses:
        overall_status = "warning"
    else:
        overall_status = "healthy"
    
    return {
        "overall_status": overall_status,
        "checks": health_checks,
        "timestamp": time.time()
    }
