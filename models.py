"""
Pydantic models for request/response validation in the video analysis API.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisType(str, Enum):
    """Analysis type enumeration."""
    LISTENING = "listening"
    RESPONDING = "responding"


# Request Models
class VideoAnalysisRequest(BaseModel):
    """Base request model for video analysis."""
    analysis_type: AnalysisType = Field(..., description="Type of analysis to perform")
    callback_url: Optional[str] = Field(None, description="Optional callback URL for async results")
    
    class Config:
        schema_extra = {
            "example": {
                "analysis_type": "listening",
                "callback_url": "https://example.com/callback"
            }
        }


class VideoPathRequest(VideoAnalysisRequest):
    """Request model for video analysis using file path."""
    video_path: str = Field(..., description="Path to the video file on server")
    
    @validator('video_path')
    def validate_video_path(cls, v):
        if not v or not v.strip():
            raise ValueError('Video path cannot be empty')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "analysis_type": "listening",
                "video_path": "/path/to/video.mp4",
                "callback_url": "https://example.com/callback"
            }
        }


# Response Models - Listening Analysis
class ScoreExplanation(BaseModel):
    """Model for score with explanation."""
    score: int = Field(..., ge=1, le=5, description="Score from 1 to 5")
    explanation: str = Field(..., description="Explanation for the score")


class ListeningEvaluation(BaseModel):
    """Listening evaluation model."""
    eyeContact: ScoreExplanation = Field(..., alias="eyeContact")
    facialExpressions: ScoreExplanation = Field(..., alias="facialExpressions")
    headShouldersAlignment: ScoreExplanation = Field(..., alias="headShouldersAlignment")
    microGestures: ScoreExplanation = Field(..., alias="microGestures")


class ListeningAnalysisResponse(BaseModel):
    """Response model for listening analysis."""
    listeningEvaluation: ListeningEvaluation
    overallScore: int = Field(..., ge=1, le=5, description="Overall score from 1 to 5")
    starRating: str = Field(..., description="Star rating representation")
    strengths: str = Field(..., description="Identified strengths")
    improvements: str = Field(..., description="Areas for improvement")
    
    class Config:
        allow_population_by_field_name = True
        schema_extra = {
            "example": {
                "listeningEvaluation": {
                    "eyeContact": {
                        "score": 4,
                        "explanation": "Good eye contact maintained throughout"
                    },
                    "facialExpressions": {
                        "score": 4,
                        "explanation": "Appropriate facial expressions showing engagement"
                    },
                    "headShouldersAlignment": {
                        "score": 3,
                        "explanation": "Generally good posture with some slouching"
                    },
                    "microGestures": {
                        "score": 4,
                        "explanation": "Good use of nodding and supportive gestures"
                    }
                },
                "overallScore": 4,
                "starRating": "★★★★☆",
                "strengths": "Strong eye contact and engagement",
                "improvements": "Focus on maintaining better posture"
            }
        }


# Response Models - Responding Analysis
class VisualEvaluation(BaseModel):
    """Visual evaluation for responding analysis."""
    eyeContact: ScoreExplanation
    facialExpressions: ScoreExplanation
    headShouldersAlignment: ScoreExplanation
    microGestures: ScoreExplanation


class VocalEvaluation(BaseModel):
    """Vocal evaluation for responding analysis."""
    toneEmotion: ScoreExplanation
    volumeClarity: ScoreExplanation
    paceRhythm: ScoreExplanation


class ContentEvaluation(BaseModel):
    """Content evaluation for responding analysis."""
    messageRelevanceAccuracy: ScoreExplanation
    structureCoherence: ScoreExplanation
    empathyWordChoice: ScoreExplanation


class RespondingEvaluation(BaseModel):
    """Responding evaluation model."""
    visual: VisualEvaluation
    vocal: VocalEvaluation
    content: ContentEvaluation


class RespondingAnalysisResponse(BaseModel):
    """Response model for responding analysis."""
    respondingEvaluation: RespondingEvaluation
    overallScore: int = Field(..., ge=1, le=5, description="Overall score from 1 to 5")
    starRating: str = Field(..., description="Star rating representation")
    strengths: str = Field(..., description="Identified strengths")
    improvements: str = Field(..., description="Areas for improvement")
    
    class Config:
        allow_population_by_field_name = True


# Generic Response Models
class VideoAnalysisResponse(BaseModel):
    """Generic video analysis response."""
    task_id: str = Field(..., description="Unique task identifier")
    analysis_type: AnalysisType = Field(..., description="Type of analysis performed")
    status: TaskStatus = Field(..., description="Current task status")
    result: Optional[Union[ListeningAnalysisResponse, RespondingAnalysisResponse]] = Field(
        None, description="Analysis result (available when status is completed)"
    )
    created_at: datetime = Field(..., description="Task creation timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if task failed")
    processing_time_seconds: Optional[float] = Field(None, description="Processing time in seconds")


class TaskStatusResponse(BaseModel):
    """Response model for task status queries."""
    task_id: str = Field(..., description="Unique task identifier")
    status: TaskStatus = Field(..., description="Current task status")
    progress: Optional[float] = Field(None, ge=0, le=100, description="Progress percentage")
    message: Optional[str] = Field(None, description="Status message")
    created_at: datetime = Field(..., description="Task creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    request_id: Optional[str] = Field(None, description="Request ID for tracking")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


class HealthCheckResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Overall health status")
    timestamp: float = Field(..., description="Health check timestamp")
    version: str = Field(..., description="API version")
    services: Dict[str, str] = Field(..., description="Status of individual services")
    uptime_seconds: Optional[float] = Field(None, description="Server uptime in seconds")


# Batch Processing Models
class BatchVideoAnalysisRequest(BaseModel):
    """Request model for batch video analysis."""
    videos: List[VideoPathRequest] = Field(..., description="List of videos to analyze")
    batch_name: Optional[str] = Field(None, description="Optional batch name")
    priority: int = Field(default=0, description="Batch priority (higher = more priority)")
    
    @validator('videos')
    def validate_videos_list(cls, v):
        if not v:
            raise ValueError('Videos list cannot be empty')
        if len(v) > 50:  # Reasonable limit
            raise ValueError('Maximum 50 videos per batch')
        return v


class BatchStatusResponse(BaseModel):
    """Response model for batch processing status."""
    batch_id: str = Field(..., description="Unique batch identifier")
    batch_name: Optional[str] = Field(None, description="Batch name")
    total_videos: int = Field(..., description="Total number of videos in batch")
    completed_videos: int = Field(..., description="Number of completed videos")
    failed_videos: int = Field(..., description="Number of failed videos")
    status: TaskStatus = Field(..., description="Overall batch status")
    progress_percentage: float = Field(..., ge=0, le=100, description="Batch progress percentage")
    created_at: datetime = Field(..., description="Batch creation timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    task_ids: List[str] = Field(..., description="List of individual task IDs in the batch")
