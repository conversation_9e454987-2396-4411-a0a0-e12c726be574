import os
from openai import OpenAI
from dotenv import load_dotenv
from constants_faisal import base_prompt_listening
import json
import gc
import time
from copy import deepcopy
import cv2
import numpy as np
import base64

load_dotenv()

# Get the API key
api_key = os.getenv('OUR_OPENAI_API_KEY')

client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY", api_key))

def read_video(video_path, max_frames=230):
    """Read video frames efficiently with adaptive sampling."""
    try:
        video = cv2.VideoCapture(video_path)
        if not video.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
            
        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = video.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"Video stats: {total_frames} frames, {fps} fps, {duration:.2f}s")
        
        # Adaptive sampling - take more frames from shorter videos
        if duration <= 30:
            frame_interval = max(5, np.ceil(total_frames / max_frames))
        elif duration <= 60:
            frame_interval = max(10, np.ceil(total_frames / max_frames))
        else:
            frame_interval = max(15, np.ceil(total_frames / max_frames))
            
        print(f"Using frame interval: {frame_interval}")
        
        base64Frames = []
        frame_count = 0
        
        # Create saved_frames directory if it doesn't exist
        save_dir = "saved_frames"
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        saved_frame_count = 0
        
        # Use JPEG encoding with reduced quality for smaller size
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 80]
        
        while video.isOpened():
            success, frame = video.read()
            if not success or len(base64Frames) >= max_frames:
                break
                
            if frame_count % int(frame_interval) == 0:
                # Resize frame to reduce memory usage
                frame = cv2.resize(frame, (0, 0), fx=0.5, fy=0.5)
                
                # Save frame to directory
                frame_filename = f"frame_{saved_frame_count:04d}.jpg"
                frame_path = os.path.join(save_dir, frame_filename)
                cv2.imwrite(frame_path, frame, encode_params)
                
                _, buffer = cv2.imencode(".jpg", frame, encode_params)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                saved_frame_count += 1
                
            frame_count += 1
            
        video.release()
        print(f"Extracted {len(base64Frames)} frames from video")
        print(f"Saved {saved_frame_count} frames to {save_dir} directory")
        return base64Frames
    except Exception as e:
        print(f"Error reading video: {str(e)}")
        if 'video' in locals() and video.isOpened():
            video.release()
        raise


response_struct_listening = json.dumps({
    "listeningEvaluation": {
        "eyeContact": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "facialExpressions": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "headShouldersAlignment": {
            "score": "integer (1-5)",
            "explanation": "string"
        },
        "microGestures": {
            "score": "integer (1-5)",
            "explanation": "string"
        }
    },
    "overallScore": "integer (1-5)",
    "starRating": "string (★★☆☆☆)",
    "strengths": "string",
    "improvements": "string"
})


def process_listening_video(base64_frames):
    print(type(base_prompt_listening))
    prompt = deepcopy(base_prompt_listening)
    prompt[-1]['content'].append(
        {
            "type": "text",
            "text": f"Please return response in json of format {response_struct_listening}. Now, Here are the video frames, audio spectrogram and transcription:\n"
        }
    )

    prompt[-1]['content'].append(
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_frames}"
            }
        }
    )

    params = {
        "model": "gpt-4o",
        "response_format": {"type": "json_object"},
        "messages": prompt,
        "max_tokens": 3000,
        "temperature": 0.0
    }

    result = client.chat.completions.create(**params)
    output_text = result.choices[0].message.content
    del prompt
    return output_text


def analyze_listening_video(video_path):
    try:
        base64_frames = read_video(video_path)
        print(len(base64_frames))
        # Process and generate the output
        output = process_listening_video(base64_frames)
        output = json.loads(output)
        return output
    finally:
        del base64_frames
        collected = gc.collect()
        print(f"Garbage collector: collected {collected} objects.")

if __name__ == "__main__":
    video_path = "0701.mp4"
    output = analyze_listening_video(video_path)
    print(output)

