"""
Test suite for the Video Analysis API.
"""

import asyncio
import json
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, Any

import httpx
import pytest
from fastapi.testclient import TestClient

# Import the FastAPI app
from main import app
from config import get_settings

# Test client
client = TestClient(app)

# Test settings
TEST_VIDEO_PATH = "test_video.mp4"  # You'll need to provide a test video
API_BASE_URL = "http://localhost:8000"


class TestVideoAnalysisAPI:
    """Test suite for the Video Analysis API."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.settings = get_settings()
    
    def test_root_endpoint(self):
        """Test the root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert data["message"] == "Video Analysis API"
    
    def test_health_check(self):
        """Test the health check endpoint."""
        response = client.get("/health")
        assert response.status_code in [200, 503]  # May fail if OpenAI key not configured
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
    
    def test_detailed_health_check(self):
        """Test the detailed health check endpoint."""
        response = client.get("/health/detailed")
        assert response.status_code in [200, 503]
        data = response.json()
        assert "overall_status" in data
        assert "checks" in data
        assert "timestamp" in data
    
    def test_stats_endpoint(self):
        """Test the stats endpoint."""
        response = client.get("/stats")
        assert response.status_code == 200
        data = response.json()
        assert "system" in data
        assert "tasks" in data
        assert "timestamp" in data
    
    def test_metrics_endpoint(self):
        """Test the metrics endpoint."""
        response = client.get("/metrics")
        # May return 404 if metrics are disabled
        assert response.status_code in [200, 404]
    
    def test_list_tasks_empty(self):
        """Test listing tasks when no tasks exist."""
        response = client.get("/tasks")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_nonexistent_task(self):
        """Test getting a task that doesn't exist."""
        response = client.get("/tasks/nonexistent-task-id")
        assert response.status_code == 404
    
    def test_cancel_nonexistent_task(self):
        """Test cancelling a task that doesn't exist."""
        response = client.delete("/tasks/nonexistent-task-id")
        assert response.status_code == 404
    
    @pytest.mark.skipif(not os.path.exists(TEST_VIDEO_PATH), reason="Test video file not available")
    def test_listening_analysis_path(self):
        """Test listening analysis with file path."""
        request_data = {
            "analysis_type": "listening",
            "video_path": TEST_VIDEO_PATH
        }
        
        response = client.post("/analyze/listening/path", json=request_data)
        assert response.status_code == 200
        data = response.json()
        
        assert "task_id" in data
        assert data["analysis_type"] == "listening"
        assert data["status"] == "pending"
        assert "created_at" in data
        
        # Check task status
        task_id = data["task_id"]
        status_response = client.get(f"/tasks/{task_id}")
        assert status_response.status_code == 200
    
    @pytest.mark.skipif(not os.path.exists(TEST_VIDEO_PATH), reason="Test video file not available")
    def test_responding_analysis_path(self):
        """Test responding analysis with file path."""
        request_data = {
            "analysis_type": "responding",
            "video_path": TEST_VIDEO_PATH
        }
        
        response = client.post("/analyze/responding/path", json=request_data)
        assert response.status_code == 200
        data = response.json()
        
        assert "task_id" in data
        assert data["analysis_type"] == "responding"
        assert data["status"] == "pending"
        assert "created_at" in data
    
    def test_file_upload_no_file(self):
        """Test file upload endpoint without providing a file."""
        response = client.post("/analyze/listening/upload")
        assert response.status_code == 422  # Validation error
    
    def test_file_upload_invalid_extension(self):
        """Test file upload with invalid file extension."""
        # Create a temporary file with invalid extension
        with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as temp_file:
            temp_file.write(b"test content")
            temp_file_path = temp_file.name
        
        try:
            with open(temp_file_path, "rb") as f:
                files = {"file": ("test.txt", f, "text/plain")}
                response = client.post("/analyze/listening/upload", files=files)
            
            assert response.status_code == 400
            assert "Unsupported file format" in response.json()["detail"]
        
        finally:
            os.unlink(temp_file_path)
    
    def test_invalid_video_path(self):
        """Test analysis with invalid video path."""
        request_data = {
            "analysis_type": "listening",
            "video_path": "/nonexistent/path/video.mp4"
        }
        
        response = client.post("/analyze/listening/path", json=request_data)
        assert response.status_code == 200  # Task is created but will fail during processing
        
        data = response.json()
        task_id = data["task_id"]
        
        # Wait a bit for processing to start and fail
        time.sleep(2)
        
        status_response = client.get(f"/tasks/{task_id}")
        assert status_response.status_code == 200
        # Task should eventually fail due to invalid path
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        # This test would need to be adjusted based on your rate limiting configuration
        # Make multiple requests quickly to trigger rate limiting
        responses = []
        for _ in range(15):  # Assuming rate limit is 10/minute
            response = client.get("/")
            responses.append(response.status_code)
        
        # Some requests should be rate limited (429 status code)
        # Note: This might not work in test environment depending on rate limiter configuration
        assert any(status == 429 for status in responses) or all(status == 200 for status in responses)


class TestAPIIntegration:
    """Integration tests that require a running server."""
    
    @pytest.mark.asyncio
    async def test_server_startup(self):
        """Test that the server starts up correctly."""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{API_BASE_URL}/health", timeout=5.0)
                assert response.status_code in [200, 503]
            except httpx.ConnectError:
                pytest.skip("Server not running for integration tests")
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test handling of concurrent requests."""
        async with httpx.AsyncClient() as client:
            try:
                # Make multiple concurrent requests
                tasks = []
                for _ in range(5):
                    task = client.get(f"{API_BASE_URL}/stats", timeout=10.0)
                    tasks.append(task)
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                # All requests should succeed
                for response in responses:
                    if isinstance(response, httpx.Response):
                        assert response.status_code == 200
                    else:
                        pytest.fail(f"Request failed: {response}")
            
            except httpx.ConnectError:
                pytest.skip("Server not running for integration tests")


def create_test_video():
    """Create a simple test video file for testing."""
    try:
        import cv2
        import numpy as np
        
        # Create a simple test video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(TEST_VIDEO_PATH, fourcc, 20.0, (640, 480))
        
        for i in range(100):  # 5 seconds at 20 fps
            # Create a simple frame with changing color
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            frame[:, :] = [i * 2 % 255, (i * 3) % 255, (i * 5) % 255]
            out.write(frame)
        
        out.release()
        print(f"Created test video: {TEST_VIDEO_PATH}")
        
    except ImportError:
        print("OpenCV not available, cannot create test video")
    except Exception as e:
        print(f"Failed to create test video: {e}")


def run_manual_tests():
    """Run manual tests that require user interaction or specific setup."""
    print("Running manual tests...")
    
    # Test server startup
    print("1. Testing server startup...")
    try:
        import subprocess
        import time
        
        # Start server in background
        process = subprocess.Popen([
            "python", "run_server.py", "--port", "8001"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(5)
        
        # Test health check
        import requests
        response = requests.get("http://localhost:8001/health", timeout=5)
        print(f"Health check status: {response.status_code}")
        
        # Cleanup
        process.terminate()
        process.wait()
        
    except Exception as e:
        print(f"Manual test failed: {e}")


if __name__ == "__main__":
    # Create test video if it doesn't exist
    if not os.path.exists(TEST_VIDEO_PATH):
        create_test_video()
    
    # Run manual tests
    run_manual_tests()
    
    # Run pytest
    pytest.main([__file__, "-v"])
