#!/usr/bin/env python3
"""
Production server startup script for the Video Analysis API.
"""

import argparse
import asyncio
import os
import sys
import signal
from pathlib import Path

import uvicorn
import structlog

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from config import get_settings, ensure_directories
from main import app

logger = structlog.get_logger()


def setup_signal_handlers():
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal", signal=signum)
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


def validate_environment():
    """Validate that all required environment variables and dependencies are available."""
    settings = get_settings()
    
    # Check OpenAI API key
    if not settings.OPENAI_API_KEY and not settings.OUR_OPENAI_API_KEY:
        logger.error("OpenAI API key not configured. Set OPENAI_API_KEY or OUR_OPENAI_API_KEY environment variable.")
        return False
    
    # Check required directories
    try:
        ensure_directories()
    except Exception as e:
        logger.error("Failed to create required directories", error=str(e))
        return False
    
    # Check ffmpeg availability (required for video processing)
    import subprocess
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("ffmpeg not found. Video processing may fail.")
    
    return True


def create_sample_env_file():
    """Create a sample .env file with all configuration options."""
    env_content = """# Video Analysis API Configuration

# Server Settings
HOST=0.0.0.0
PORT=8000
DEBUG=false

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
# Alternative key name (for backward compatibility)
OUR_OPENAI_API_KEY=your_openai_api_key_here

# Security Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_HOSTS=["localhost", "127.0.0.1", "*"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=10
RATE_LIMIT_PER_HOUR=100

# File Upload Settings
MAX_FILE_SIZE=104857600  # 100MB in bytes
ALLOWED_VIDEO_EXTENSIONS=[".mp4", ".avi", ".mov", ".mkv", ".webm"]
UPLOAD_DIR=uploads
TEMP_DIR=temp

# Processing Settings
MAX_CONCURRENT_TASKS=5
TASK_TIMEOUT_SECONDS=300  # 5 minutes
CLEANUP_INTERVAL_SECONDS=3600  # 1 hour

# Video Processing Settings
MAX_FRAMES=230
FRAME_QUALITY=80
FRAME_RESIZE_FACTOR=0.5

# OpenAI Settings
OPENAI_MAX_RETRIES=3
OPENAI_TIMEOUT_SECONDS=60

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=json

# Redis Settings (for production)
REDIS_URL=redis://localhost:6379/0
USE_REDIS=false

# Monitoring Settings
ENABLE_METRICS=true
METRICS_PORT=8001
"""
    
    with open(".env.example", "w") as f:
        f.write(env_content)
    
    logger.info("Created .env.example file with sample configuration")


def main():
    """Main entry point for the server."""
    parser = argparse.ArgumentParser(description="Video Analysis API Server")
    parser.add_argument("--host", default=None, help="Host to bind to")
    parser.add_argument("--port", type=int, default=None, help="Port to bind to")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload for development")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker processes")
    parser.add_argument("--log-level", default=None, help="Log level (debug, info, warning, error)")
    parser.add_argument("--create-env", action="store_true", help="Create sample .env file and exit")
    parser.add_argument("--validate", action="store_true", help="Validate configuration and exit")
    
    args = parser.parse_args()
    
    # Create sample env file if requested
    if args.create_env:
        create_sample_env_file()
        return
    
    # Load settings
    settings = get_settings()
    
    # Validate environment
    if not validate_environment():
        logger.error("Environment validation failed")
        sys.exit(1)
    
    if args.validate:
        logger.info("Environment validation passed")
        return
    
    # Setup signal handlers
    setup_signal_handlers()
    
    # Determine server configuration
    host = args.host or settings.HOST
    port = args.port or settings.PORT
    log_level = args.log_level or settings.LOG_LEVEL.lower()
    
    # Configure uvicorn
    uvicorn_config = {
        "app": "main:app",
        "host": host,
        "port": port,
        "log_level": log_level,
        "access_log": True,
        "reload": args.reload or settings.DEBUG,
        "workers": 1 if args.reload or settings.DEBUG else args.workers,
    }
    
    logger.info(
        "Starting Video Analysis API server",
        host=host,
        port=port,
        workers=uvicorn_config["workers"],
        reload=uvicorn_config["reload"],
        log_level=log_level
    )
    
    # Start server
    try:
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
    except Exception as e:
        logger.error("Server startup failed", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
