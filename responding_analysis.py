import os
from openai import OpenAI
from dotenv import load_dotenv
from constants_faisal import base_prompt_responding
import json
import gc
from copy import deepcopy
import cv2
import numpy as np
import base64
import tempfile
import subprocess
from pydub import AudioSegment
import librosa
import noisereduce as nr
import matplotlib.pyplot as plt

load_dotenv()

# Get the API key
api_key = os.getenv('OUR_OPENAI_API_KEY')

client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY", api_key))

def read_video(video_path, max_frames=230):
    """Read video frames efficiently with adaptive sampling."""
    try:
        video = cv2.VideoCapture(video_path)
        if not video.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
            
        total_frames = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = video.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        
        print(f"Video stats: {total_frames} frames, {fps} fps, {duration:.2f}s")
        
        # Adaptive sampling - take more frames from shorter videos
        if duration <= 30:
            frame_interval = max(5, np.ceil(total_frames / max_frames))
        elif duration <= 60:
            frame_interval = max(10, np.ceil(total_frames / max_frames))
        else:
            frame_interval = max(15, np.ceil(total_frames / max_frames))
            
        print(f"Using frame interval: {frame_interval}")
        
        base64Frames = []
        frame_count = 0
        
        # Use JPEG encoding with reduced quality for smaller size
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 80]
        
        while video.isOpened():
            success, frame = video.read()
            if not success or len(base64Frames) >= max_frames:
                break
                
            if frame_count % int(frame_interval) == 0:
                # Resize frame to reduce memory usage
                frame = cv2.resize(frame, (0, 0), fx=0.5, fy=0.5)
                _, buffer = cv2.imencode(".jpg", frame, encode_params)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))
                
            frame_count += 1
            
        video.release()
        print(f"Extracted {len(base64Frames)} frames from video")
        return base64Frames
    except Exception as e:
        print(f"Error reading video: {str(e)}")
        if 'video' in locals() and video.isOpened():
            video.release()
        raise



def extract_audio_from_video(video_path):
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio_file:
        audio_output_path = temp_audio_file.name
    command = f"ffmpeg -i -loglevel quiet \"{video_path}\" -q:a 0 -map a \"{audio_output_path}\" -y"
    subprocess.call(command, shell=True)
    print(f"Audio extracted and saved to {audio_output_path}")
    return audio_output_path


def transcribe_audio(input_audio_path):
    with open(input_audio_path, "rb") as audio_file:
        transcription = client.audio.transcriptions.create(
            model="whisper-1",
            language="en",
            file=audio_file,
            response_format="json"
        )
        # print(transcription)
    return transcription.text


def is_audio_empty(audio_path, silence_threshold=-50.0):
    audio = AudioSegment.from_wav(audio_path)
    # Measure the loudness in dBFS (decibels relative to full scale)
    loudness = audio.dBFS
    # If the loudness is below the threshold, consider the audio as empty
    return loudness < silence_threshold


def save_spectrogram(audio_path):
    y, sr = librosa.load(audio_path, sr=None)
    reduced_noise = nr.reduce_noise(y=y, sr=sr)
    S = librosa.feature.melspectrogram(y=reduced_noise, sr=sr, n_mels=128)
    S_dB = librosa.power_to_db(S, ref=np.max)

    plt.figure(figsize=(10, 4))
    librosa.display.specshow(S_dB, sr=sr, x_axis='time', y_axis='mel')
    plt.colorbar(format='%+2.0f dB')
    plt.title('Mel-frequency spectrogram')

    output_path = os.path.splitext(audio_path)[0] + ".png"
    plt.savefig(output_path)
    plt.close()
    with open(output_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')
    

response_struct_responding = json.dumps({
    "respondingEvaluation": {
        "visual": {
            "eyeContact": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "facialExpressions": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "headShouldersAlignment": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "microGestures": {
                "score": "integer (1-5)",
                "explanation": "string"
            }
        },
        "vocal": {
            "toneEmotion": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "volumeClarity": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "paceRhythm": {
                "score": "integer (1-5)",
                "explanation": "string"
            }
        },
        "content": {
            "messageRelevanceAccuracy": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "structureCoherence": {
                "score": "integer (1-5)",
                "explanation": "string"
            },
            "empathyWordChoice": {
                "score": "integer (1-5)",
                "explanation": "string"
            }
        }
    },
    "overallScore": "integer (1-5)",
    "starRating": "string (★★★★☆)",
    "strengths": "string",
    "improvements": "string"
})


def process_responding_video(base64_frames, transcription_text, spectrogram_image):
    print(type(base_prompt_responding))
    prompt = deepcopy(base_prompt_responding)
    prompt[-1]['content'].append(
        {
            "type": "text",
            "text": f"Please return response in json of format {response_struct_responding}. Now, Here are the video frames, audio spectrogram and transcription:\n"
        }
    )

    prompt[-1]['content'].append(
        {
            "type": "text",
            "text": f"Transcription: \"{transcription_text}\"\n"
        }
    )

    prompt[-1]['content'].append(
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{spectrogram_image}"
            }
        }
    )

    prompt[-1]['content'].append(
        {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_frames}"
            }
        }
    )

    params = {
        "model": "gpt-4o",
        "response_format": {"type": "json_object"},
        "messages": prompt,
        "max_tokens": 3000,
        "temperature": 0.0
    }

    result = client.chat.completions.create(**params)
    output_text = result.choices[0].message.content
    del prompt
    return output_text


def analyze_responding_video(video_path):
    try:
        base64_frames = read_video(video_path)
        print(len(base64_frames))
        # Process and generate the output
        audio_path = extract_audio_from_video(video_path)
        transcription_text = transcribe_audio(audio_path)
        spectrogram_image = save_spectrogram(audio_path)
        output = process_responding_video(base64_frames, transcription_text, spectrogram_image)
        output = json.loads(output)
        return output
    finally:
        del base64_frames
        del audio_path
        del spectrogram_image
        del transcription_text
        collected = gc.collect()
        print(f"Garbage collector: collected {collected} objects.")

if __name__ == "__main__":
    video_path = "Ali-bad.mp4"
    output = analyze_responding_video(video_path)
    print(output)

