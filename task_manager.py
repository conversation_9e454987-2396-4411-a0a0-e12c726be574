"""
Task management and cleanup utilities for the video analysis API.
"""

import asyncio
import time
from typing import Dict, Any, List
import structlog
from config import get_settings

logger = structlog.get_logger()


class TaskManager:
    """Manages video analysis tasks and cleanup operations."""
    
    def __init__(self, task_storage: Dict[str, Dict[str, Any]]):
        self.task_storage = task_storage
        self.settings = get_settings()
        self._cleanup_task = None
        self._running = False
    
    async def start_cleanup_service(self):
        """Start the background cleanup service."""
        if self._cleanup_task is not None:
            return
        
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("Task cleanup service started")
    
    async def stop_cleanup_service(self):
        """Stop the background cleanup service."""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
        logger.info("Task cleanup service stopped")
    
    async def _cleanup_loop(self):
        """Background loop for cleaning up old tasks."""
        while self._running:
            try:
                await self.cleanup_old_tasks()
                await asyncio.sleep(self.settings.CLEANUP_INTERVAL_SECONDS)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error("Error in cleanup loop", error=str(e))
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def cleanup_old_tasks(self):
        """Clean up old completed/failed tasks."""
        current_time = time.time()
        cleanup_threshold = current_time - (24 * 3600)  # 24 hours ago
        
        tasks_to_remove = []
        
        for task_id, task_data in self.task_storage.items():
            # Only cleanup completed, failed, or cancelled tasks
            if task_data["status"] not in ["completed", "failed", "cancelled"]:
                continue
            
            # Check if task is old enough to cleanup
            completed_at = task_data.get("completed_at", task_data.get("created_at", current_time))
            if completed_at < cleanup_threshold:
                tasks_to_remove.append(task_id)
        
        # Remove old tasks
        for task_id in tasks_to_remove:
            try:
                task_data = self.task_storage.pop(task_id, None)
                if task_data:
                    logger.info("Cleaned up old task", task_id=task_id, status=task_data["status"])
            except Exception as e:
                logger.error("Error removing old task", task_id=task_id, error=str(e))
        
        if tasks_to_remove:
            logger.info("Cleanup completed", removed_tasks=len(tasks_to_remove))
    
    def get_task_statistics(self) -> Dict[str, Any]:
        """Get current task statistics."""
        stats = {
            "total_tasks": len(self.task_storage),
            "by_status": {},
            "by_analysis_type": {},
            "oldest_task_age_hours": 0,
            "newest_task_age_hours": 0
        }
        
        if not self.task_storage:
            return stats
        
        current_time = time.time()
        
        # Count by status and analysis type
        for task_data in self.task_storage.values():
            status = task_data["status"]
            analysis_type = task_data["analysis_type"]
            
            stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
            stats["by_analysis_type"][analysis_type] = stats["by_analysis_type"].get(analysis_type, 0) + 1
        
        # Calculate age statistics
        creation_times = [task_data["created_at"] for task_data in self.task_storage.values()]
        if creation_times:
            oldest_time = min(creation_times)
            newest_time = max(creation_times)
            
            stats["oldest_task_age_hours"] = (current_time - oldest_time) / 3600
            stats["newest_task_age_hours"] = (current_time - newest_time) / 3600
        
        return stats
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """Get list of currently active (pending/processing) tasks."""
        active_tasks = []
        
        for task_id, task_data in self.task_storage.items():
            if task_data["status"] in ["pending", "processing"]:
                active_tasks.append({
                    "task_id": task_id,
                    "status": task_data["status"],
                    "analysis_type": task_data["analysis_type"],
                    "created_at": task_data["created_at"],
                    "started_at": task_data.get("started_at"),
                    "age_seconds": time.time() - task_data["created_at"]
                })
        
        # Sort by creation time (oldest first)
        active_tasks.sort(key=lambda x: x["created_at"])
        
        return active_tasks
    
    async def cancel_stuck_tasks(self):
        """Cancel tasks that have been processing for too long."""
        current_time = time.time()
        timeout_threshold = current_time - self.settings.TASK_TIMEOUT_SECONDS
        
        stuck_tasks = []
        
        for task_id, task_data in self.task_storage.items():
            if task_data["status"] == "processing":
                started_at = task_data.get("started_at", task_data["created_at"])
                if started_at < timeout_threshold:
                    stuck_tasks.append(task_id)
        
        # Cancel stuck tasks
        for task_id in stuck_tasks:
            try:
                task_data = self.task_storage[task_id]
                task_data.update({
                    "status": "failed",
                    "error_message": "Task timed out",
                    "completed_at": current_time,
                    "processing_time": current_time - task_data.get("started_at", task_data["created_at"])
                })
                
                logger.warning("Cancelled stuck task", task_id=task_id, 
                             processing_time=task_data["processing_time"])
                
            except Exception as e:
                logger.error("Error cancelling stuck task", task_id=task_id, error=str(e))
        
        if stuck_tasks:
            logger.info("Cancelled stuck tasks", count=len(stuck_tasks))
        
        return len(stuck_tasks)
    
    async def get_task_queue_info(self) -> Dict[str, Any]:
        """Get information about the current task queue."""
        active_tasks = self.get_active_tasks()
        stats = self.get_task_statistics()
        
        # Calculate average processing time for completed tasks
        completed_tasks = [
            task_data for task_data in self.task_storage.values()
            if task_data["status"] == "completed" and "processing_time" in task_data
        ]
        
        avg_processing_time = 0
        if completed_tasks:
            total_time = sum(task["processing_time"] for task in completed_tasks)
            avg_processing_time = total_time / len(completed_tasks)
        
        # Estimate queue processing time
        pending_count = stats["by_status"].get("pending", 0)
        estimated_queue_time = pending_count * avg_processing_time if avg_processing_time > 0 else 0
        
        return {
            "active_tasks": len(active_tasks),
            "pending_tasks": pending_count,
            "processing_tasks": stats["by_status"].get("processing", 0),
            "average_processing_time_seconds": avg_processing_time,
            "estimated_queue_time_seconds": estimated_queue_time,
            "max_concurrent_tasks": self.settings.MAX_CONCURRENT_TASKS,
            "task_timeout_seconds": self.settings.TASK_TIMEOUT_SECONDS,
            "oldest_pending_task_age_seconds": (
                min([task["age_seconds"] for task in active_tasks if task["status"] == "pending"], default=0)
            )
        }


# Rate limiting utilities
class RateLimitManager:
    """Manages rate limiting and request throttling."""
    
    def __init__(self):
        self.request_counts = {}
        self.settings = get_settings()
    
    def is_rate_limited(self, client_ip: str) -> bool:
        """Check if a client IP is rate limited."""
        current_time = time.time()
        
        # Clean old entries
        self._cleanup_old_entries(current_time)
        
        # Check current rate
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = []
        
        client_requests = self.request_counts[client_ip]
        
        # Count requests in the last minute
        minute_ago = current_time - 60
        recent_requests = [req_time for req_time in client_requests if req_time > minute_ago]
        
        return len(recent_requests) >= self.settings.RATE_LIMIT_PER_MINUTE
    
    def record_request(self, client_ip: str):
        """Record a request from a client IP."""
        current_time = time.time()
        
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = []
        
        self.request_counts[client_ip].append(current_time)
    
    def _cleanup_old_entries(self, current_time: float):
        """Clean up old request entries."""
        hour_ago = current_time - 3600
        
        for client_ip in list(self.request_counts.keys()):
            self.request_counts[client_ip] = [
                req_time for req_time in self.request_counts[client_ip]
                if req_time > hour_ago
            ]
            
            # Remove empty entries
            if not self.request_counts[client_ip]:
                del self.request_counts[client_ip]
    
    def get_rate_limit_info(self, client_ip: str) -> Dict[str, Any]:
        """Get rate limit information for a client IP."""
        current_time = time.time()
        
        if client_ip not in self.request_counts:
            return {
                "requests_last_minute": 0,
                "requests_last_hour": 0,
                "limit_per_minute": self.settings.RATE_LIMIT_PER_MINUTE,
                "limit_per_hour": self.settings.RATE_LIMIT_PER_HOUR,
                "is_limited": False
            }
        
        client_requests = self.request_counts[client_ip]
        
        minute_ago = current_time - 60
        hour_ago = current_time - 3600
        
        requests_last_minute = len([req for req in client_requests if req > minute_ago])
        requests_last_hour = len([req for req in client_requests if req > hour_ago])
        
        return {
            "requests_last_minute": requests_last_minute,
            "requests_last_hour": requests_last_hour,
            "limit_per_minute": self.settings.RATE_LIMIT_PER_MINUTE,
            "limit_per_hour": self.settings.RATE_LIMIT_PER_HOUR,
            "is_limited": requests_last_minute >= self.settings.RATE_LIMIT_PER_MINUTE
        }
