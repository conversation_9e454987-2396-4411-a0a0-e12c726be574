#!/usr/bin/env python3
"""
Debug script to check import issues and dependencies.
"""

import sys
import os
import traceback

def check_import(module_name, description=""):
    """Check if a module can be imported."""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - OK {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - FAILED: {e} {description}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - ERROR: {e} {description}")
        return False

def check_environment():
    """Check environment variables and settings."""
    print("\n🔧 Environment Check:")
    
    # Check Python version
    print(f"Python version: {sys.version}")
    
    # Check environment variables
    env_vars = [
        "OPENAI_API_KEY",
        "OUR_OPENAI_API_KEY", 
        "NUMBA_CACHE_DIR",
        "NUMBA_DISABLE_JIT"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Mask API keys for security
            if "API_KEY" in var:
                masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"  {var}: {masked_value}")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: Not set")

def check_system_dependencies():
    """Check system dependencies."""
    print("\n🖥️  System Dependencies:")
    
    # Check ffmpeg
    try:
        import subprocess
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ ffmpeg - {version_line}")
        else:
            print("❌ ffmpeg - Not working properly")
    except FileNotFoundError:
        print("❌ ffmpeg - Not found")
    except Exception as e:
        print(f"⚠️  ffmpeg - Error checking: {e}")

def main():
    """Main debug function."""
    print("🔍 Video Analysis API - Import Debug")
    print("=" * 50)
    
    # Check environment
    check_environment()
    
    # Check system dependencies
    check_system_dependencies()
    
    print("\n📦 Python Package Imports:")
    
    # Core dependencies
    core_packages = [
        ("fastapi", "Web framework"),
        ("uvicorn", "ASGI server"),
        ("pydantic", "Data validation"),
        ("structlog", "Logging"),
        ("openai", "OpenAI API client"),
        ("python_dotenv", "Environment variables"),
    ]
    
    for package, desc in core_packages:
        check_import(package, f"- {desc}")
    
    print("\n🎥 Video Processing:")
    video_packages = [
        ("cv2", "OpenCV - Video processing"),
        ("numpy", "NumPy - Array operations"),
        ("PIL", "Pillow - Image processing"),
    ]
    
    for package, desc in video_packages:
        check_import(package, f"- {desc}")
    
    print("\n🔊 Audio Processing:")
    audio_packages = [
        ("pydub", "Audio manipulation"),
        ("librosa", "Audio analysis"),
        ("noisereduce", "Noise reduction"),
        ("matplotlib", "Plotting"),
    ]
    
    for package, desc in audio_packages:
        check_import(package, f"- {desc}")
    
    print("\n🧠 Analysis Modules:")
    
    # Check custom analysis modules
    try:
        print("Checking listening_analysis...")
        import listening_analysis
        print("✅ listening_analysis - OK")
        
        # Try to import the main function
        from listening_analysis import analyze_listening_video
        print("✅ analyze_listening_video function - OK")
        
    except Exception as e:
        print(f"❌ listening_analysis - FAILED: {e}")
        traceback.print_exc()
    
    try:
        print("Checking responding_analysis...")
        import responding_analysis
        print("✅ responding_analysis - OK")
        
        # Try to import the main function
        from responding_analysis import analyze_responding_video
        print("✅ analyze_responding_video function - OK")
        
    except Exception as e:
        print(f"❌ responding_analysis - FAILED: {e}")
        traceback.print_exc()
    
    print("\n🚀 FastAPI Application:")
    
    # Check if the main app can be imported
    try:
        from main import app
        print("✅ FastAPI app - OK")
    except Exception as e:
        print(f"❌ FastAPI app - FAILED: {e}")
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("Debug complete! Check the results above for any issues.")

if __name__ == "__main__":
    main()
